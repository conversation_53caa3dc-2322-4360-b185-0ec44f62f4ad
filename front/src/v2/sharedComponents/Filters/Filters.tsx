import React, { useContext, useRef } from 'react'
import PropTypes from 'prop-types'

import FiltersContext from 'src/v2/contexts/FiltersContext'

import { useReSizeFilterBar } from './hooks/useReSizeFilterBar'
import { useRepositionScroll } from './hooks/useRepositionScroll'

import FilterPanel from './components/FilterPanel/FilterPanel'
import Text from '../../sharedComponents/Text/Text'
import { Loader } from 'src/v2/sharedComponentStyles/Loader/Loader'
import {
  SideBarFilters,
  FilterContainer,
  HeaderFilter,
  TitleHeader,
  ContainerApplyButton,
  StickyContainer
} from './FilterStyled'
import ApplyFilterButton from './components/ApplyFilterButton/ApplyFilterButton'
import ResetFilterButton from './components/ResetFilterButton/ResetFilterButton'

const Filters: React.FC = (_, context: any) => {
  const { filters, loading, hasOpenPanel } = useContext(FiltersContext)
  const sidebarFilterRef = useRef<HTMLElement>(null)
  const filterContainerRef = useRef<HTMLDivElement>(null)

  const height = useReSizeFilterBar(sidebarFilterRef)
  const { scrollToSidebar, restarInnerScroll } = useRepositionScroll({ sidebarFilterRef, filterContainerRef })

  if (loading) {
    return (
      <SideBarFilters>
        <Loader />
      </SideBarFilters>
    )
  }

  if (!loading && (filters && filters.length === 0)) {
    return (
      <SideBarFilters>
        <HeaderFilter>
          <TitleHeader>{context.t('filters')}</TitleHeader>
        </HeaderFilter>
        <FilterContainer>
          <Text size={16} color="carbon">{context.t('noFiltersAvailable')}</Text>
        </FilterContainer>
      </SideBarFilters>
    )
  }

  return (
    <StickyContainer>
      <SideBarFilters ref={sidebarFilterRef} height={height} >
        <HeaderFilter>
          <TitleHeader>{context.t('filters')}</TitleHeader>
          <ResetFilterButton handleClick={() => scrollToSidebar()}/>
        </HeaderFilter>
        <FilterContainer ref={filterContainerRef} hasOpenPanel={hasOpenPanel}>
          <FilterPanel restartParentScroll={restarInnerScroll} />
        </FilterContainer>
        <ContainerApplyButton height={height}>
          <ApplyFilterButton handleClick={() => scrollToSidebar()}/>
        </ContainerApplyButton>
      </SideBarFilters>
    </StickyContainer>
  )
}

Filters.contextTypes = {
  t: PropTypes.func.isRequired
}

export default Filters
