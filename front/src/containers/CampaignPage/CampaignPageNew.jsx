import React, { Component } from 'react'
import * as _ from 'lodash'
import * as actions from '../../actions/fetchActions'
import { connect } from 'react-redux'
import queryString from 'query-string'

import { PropTypes } from 'prop-types'
import {
  getLanguageIdByCode,
  getLanguageCodeById,
  isCampaignEmbargoed,
  analyzeUrl
} from '../../helpers'
import RsTooltip from '../../components/RsTooltip'

import { getCookies } from '../../Cookies'

import {
  Assets,
  Carrousel,
  Icon,
  RsButtonShare,
  RsLoader,
  RsModal,
  RsSelect,
  Text,
  NewText,
  GenericModal,
  Title
} from '../../components'

var HtmlToReactParser = require('html-to-react').Parser

import AdobeAnalytics from 'src/v2/intances/AdobeAnalytics'
import Filters from 'src/v2/sharedComponents/Filters/Filters'
import { FiltersContextProvider } from 'src/v2/contexts/FiltersContext'
import FiltersChips from 'src/v2/sharedComponents/Filters/FiltersChips/FiltersChips'
import YourSearchResults from 'src/v2/sharedComponents/yourSearchResults/YourSearchResults'
import {
  LibrarySearchBarContainer,
  SearchBarLibraryStyles
} from 'src/v2/sharedComponents/SearchBarLibrary/SearchBarLibraryStyles'
import SearchTip from 'src/v2/sharedComponents/SearchBar/components/searchTip/SearchTip'
import SearchSuggestions from 'src/v2/sharedComponents/SearchBar/components/searchSuggestions/SearchSuggestions'
import { GoBackArrowButtonSafe } from 'src/v2/sharedComponents/GoBackButton/GoBackArrowButton'

const { eventDispatch } = AdobeAnalytics

class CampaignPageNew extends Component {
  constructor(props) {
    super(props)
    this.state = {
      campaign: {},
      show: true,
      previewModal: false,
      displayModalDisclaimer: false,
      bookmark: 0,
      isTooltipMessage: false,
      relatedCampaigns: [],
      campaignId: null,
      urlLanguage: null,
      isModalVisible: false,
      currentContent: '',
      modalTitle: '',
      modalLinksDefined: false,
      title: '',
      _isUpdatingFromCampaignChange: false,
      isSearching: false
    }

    this.campaign = null
    this.typeMaterial = null
    this.actionBookmark = this.actionBookmark.bind(this)
    this.htmlDescriptionRef = React.createRef()
    this.htmlMainBenefitsRef = React.createRef()
    this.handleCancel = this.handleCancel.bind(this)
  }

  showModal = (text, title) => {
    this.setState({ ...this.state, currentContent: text, isModalVisible: true, title: title })
  }

  handleCancel() {
    this.setState({ ...this.state, isModalVisible: false })
  }

  renderizeModal = () => {
    if (this.htmlDescriptionRef.current) {
      const elements = this.htmlDescriptionRef.current.children
      const linkModalElements = [].slice.call(elements)

      if (this.htmlMainBenefitsRef.current && this.htmlMainBenefitsRef.current.children) {
        const elementsMainBenefits = this.htmlMainBenefitsRef.current.children
        const linkModalElementsMainBenefits = [].slice.call(elementsMainBenefits)

        linkModalElementsMainBenefits.map((element) => {
          const elModalLink = element.querySelector('.link-modal')

          if (elModalLink) {
            elModalLink.addEventListener('click', (e) => {
              e.preventDefault()
              const element = e.target
              this.showModal(element.getAttribute('data-modal'), element.innerText)
            })
          }
          return undefined
        })
      }

      linkModalElements.map((element) => {
        const elModalLink = element.querySelectorAll('.link-modal')

        if (elModalLink) {
          elModalLink.forEach((link) => {
            link.addEventListener('click', (e) => {
              e.preventDefault()
              const element = e.target
              this.showModal(element.getAttribute('data-modal'), element.innerText)
            })
          })
        }
        return undefined
      })
    }
  }

  componentDidMount() {
    this.props.cleanSearchValue()
    this.props.cleanFullCampaign()
    this.props.cleanFilterApplied()

    // Obtiene campaignId y langId con el idlang de la url [idlang = 224EN]
    const { campaignId, langId } = this.getUrlParams(this.props.match.params.idlang)

    // Setea el estado con campaignId y langId y ejecuta analyzeUrlandSetProps
    this.setState({ urlLanguage: langId, campaignId }, () => {
      this.analyzeUrlandSetProps()
    })

    // Fetch campaign detail
    this.props.fetchCampaignDetail(campaignId)
  }

  getUrlParams = (urlParams) => {
    let separatedQueryStrings = urlParams.match(/[0-9]+|[^0-9]+/gi)
    let campaignId = +separatedQueryStrings[0]
    let langCode = separatedQueryStrings[1]
    let langId = getLanguageIdByCode(langCode.toUpperCase())

    return { campaignId, langCode, langId }
  }

  checkSecondaryCampaigns = (fullDetailsCampaign, newUrlLanguage) => {
    const descriptions = fullDetailsCampaign.description
    const urlLanguage = newUrlLanguage ? newUrlLanguage : this.state.urlLanguage

    const englishLanguageId = 1
    const languageCampaignDefault = descriptions.find(
      (desc) => desc.language_id === englishLanguageId
    )

    const campaignInfoByLanguage = descriptions.find(
      (desc) => desc.language_id === this.props.preferences.ui_language_id && desc.active
    )
    let campaignInfo = campaignInfoByLanguage || languageCampaignDefault

    const languageCampaignPreview = descriptions.find(
      (desc) => desc.language_id === urlLanguage && desc.active
    )

    const campaignPreviewLanguageIfDefaultNotActive = this.sortLanguagesByName(
      fullDetailsCampaign.languages.filter((l) => l.active === 1)
    )[0].id
    const languageCampaignPreviewIfDefaultNotActive = descriptions.find(
      (desc) => desc.language_id === campaignPreviewLanguageIfDefaultNotActive
    )

    let campaignPreview =
      languageCampaignPreview ||
      (languageCampaignDefault.active === 1
        ? languageCampaignDefault
        : languageCampaignPreviewIfDefaultNotActive)

    this.setState({ campaignInfo, campaignPreview, urlLanguage: campaignPreview.language_id })

    if (!campaignInfo) {
      this.props.history.replace('/unavailableContent')
    } else if (!campaignPreview) {
      this.props.history.replace(
        `/campaign/CA${this.state.campaignId}EN${this.props.location.search}`
      )
    } else {
      // Get existing filters from URL
      const { filtersApplied } = analyzeUrl(this.props.location.search)

      // Construct new filters preserving existing ones
      const newFilters = JSON.stringify({
        ...(filtersApplied || {}),
        campaign: { value: [this.state.campaignId] },
        language: { value: [campaignPreview.language_id] }
      })

      const { searchString } = analyzeUrl(this.props.location.search)
      const searchParam = searchString ? `&search=${encodeURIComponent(searchString)}` : ''

      // Replace URL with proper filters
      this.props.history.replace(
        `/campaign/CA${this.state.campaignId}${getLanguageCodeById(
          campaignPreview.language_id
        ).toUpperCase()}?type=1&filters=${newFilters}${searchParam}`
      )

      // Trigger a new search with updated filters
      this.analyzeUrlandSetProps()
    }
  }

  checkCampaignLanguages = (infoLanguage, previewLanguage) => {
    const campaignDescriptions = this.props.fullDetailsCampaign.description

    let campaignInfo =
      campaignDescriptions.find((desc) => desc.language_id === infoLanguage && desc.active === 1) ||
      campaignDescriptions.find((desc) => desc.language_id === 1)

    const campaignPreviewLanguageIfDefaultNotActive = this.sortLanguagesByName(
      this.props.fullDetailsCampaign.languages.filter((l) => l.active === 1)
    )[0].id
    const campaignPreviewIfDefaultNotActive = campaignDescriptions.find(
      (desc) => desc.language_id === campaignPreviewLanguageIfDefaultNotActive
    )

    let campaignPreview =
      campaignDescriptions.find(
        (desc) => desc.language_id === previewLanguage && desc.active === 1
      ) ||
      campaignDescriptions.find((desc) => desc.language_id === 1 && desc.active === 1) ||
      campaignPreviewIfDefaultNotActive

    return { campaignInfo, campaignPreview }
  }

  resolveLanguages = (campaignInfo, campaignPreview) => {
    if (!campaignInfo || !campaignPreview) {
      this.props.history.replace('/unavailableContent')
    } else {
      // // Existe CampaignInfo y CampaignPreview, aplicar filtro de campaign y de idioma, y pushear a url
      const urlLanguage = campaignPreview.language_id

      // Get existing filters from URL to preserve them
      const { searchString, filtersApplied } = analyzeUrl(this.props.location.search)

      const newFilters = JSON.stringify({
        ...(filtersApplied || {}), // Preserve existing filters
        campaign: { value: [this.state.campaignId] },
        language: { value: [urlLanguage] }
      })

      const searchParam = searchString ? `&search=${encodeURIComponent(searchString)}` : ''

      this.setState({ campaignInfo, campaignPreview, urlLanguage })
      this.props.history.replace(
        `/campaign/CA${this.state.campaignId}${getLanguageCodeById(
          urlLanguage
        ).toUpperCase()}?type=1&filters=${newFilters}${searchParam}`
      )
    }
  }

  componentDidUpdate(prevProps, prevState) {
    if (this.htmlDescriptionRef.current) {
      this.renderizeModal()
    }

    // When URL search params change
    if (prevProps.location.search !== this.props.location.search) {
      const prevValues = queryString.parse(prevProps.location.search)
      const values = queryString.parse(this.props.location.search)

      // Only trigger search if the change wasn't caused by checkSecondaryCampaigns
      if (!this.state._isUpdatingFromCampaignChange) {
        // Always trigger search when search parameter changes
        if (values.search !== prevValues.search) {
          this.analyzeUrlandSetProps()
        }
        // For other parameters, only trigger if not caused by initial setup
        else if (prevValues.filters !== values.filters || prevValues.type !== values.type) {
          this.analyzeUrlandSetProps()
        }
      }
    }

    // Update loading state when assets change
    if (prevProps.assets !== this.props.assets && this.state.isSearching) {
      this.setState({ isSearching: false })
    }

    // When campaign data changes
    if (
      this.props.fullDetailsCampaign !== undefined &&
      JSON.stringify(this.props.fullDetailsCampaign) !== '{}'
    ) {
      if (!this.props.fullDetailsCampaign) {
        this.props.history.replace('/404')
      } else if (!this.props.fullDetailsCampaign.active) {
        this.props.history.replace('/unavailableContent')
      } else if (
        this.props.fullDetailsCampaign.active &&
        !this.props.fullDetailsCampaign.available
      ) {
        this.props.history.replace('/restrictedContent')
      } else {
        // // Cuando se carga la campaña después del fetch, por primera vez:
        if (
          _.isEmpty(prevProps.fullDetailsCampaign) &&
          !_.isEmpty(this.props.fullDetailsCampaign)
        ) {
          this.state._isUpdatingFromCampaignChange = true
          this.checkSecondaryCampaigns(this.props.fullDetailsCampaign)
          this.setState({
            bookmark: this.props.fullDetailsCampaign.bookmark,
            relatedCampaigns: this.props.fullDetailsCampaign.relatedCampaigns
          })
          this.state._isUpdatingFromCampaignChange = false
        }
      }
    }

    // // Cuando cambia el idioma de la UI desde props
    if (!_.isEqual(prevProps.lang, this.props.lang) && !_.isEmpty(this.props.fullDetailsCampaign)) {
      let newInfoLanguage, newPreviewLanguage
      newInfoLanguage = newPreviewLanguage = getLanguageIdByCode(this.props.lang)

      let { campaignInfo, campaignPreview } = this.checkCampaignLanguages(
        newInfoLanguage,
        newPreviewLanguage
      )

      this.resolveLanguages(campaignInfo, campaignPreview)
    }

    // // Cuando cambia el idioma de la url
    if (!_.isEqual(prevProps.match.params.idlang, this.props.match.params.idlang)) {
      let { langId } = this.getUrlParams(this.props.match.params.idlang)
      this.setState({ urlLanguage: langId })

      this.checkSecondaryCampaigns(this.props.fullDetailsCampaign, langId)
    }
  }

  analyzeUrlandSetProps = () => {
    const { searchString, filtersApplied, materialType, ...rest } = analyzeUrl(
      this.props.location.search
    )

    // Preserve existing filters and ensure campaign and language are set
    const campaignFilters = {
      ...(filtersApplied || {}), // Keep all existing filters
      campaign: { value: [this.state.campaignId] }, // Ensure campaign is set
      language: { value: [this.state.urlLanguage || this.props.preferences.ui_language_id] } // Ensure language is set
    }

    // Extract language ID from filters for backend query
    // Use the language from the updated campaignFilters (which includes applied filters from URL)
    const languageForQuery =
      campaignFilters?.language?.value?.[0] || this.props.preferences.ui_language_id

    // Set props
    this.props.setSearchValue(searchString || '')
    this.props.updateAppliedFilters(campaignFilters)

    // Execute Search
    this.executeSearch(
      searchString,
      materialType || 1, // Default to type 1 if not specified
      campaignFilters,
      this.props.sortBy,
      languageForQuery
    )
  }

  executeSearch = (
    searchString,
    materialType,
    filtersApplied,
    sortBy,
    UILanguageId,
    options = {}
  ) => {
    this.setState({ isSearching: true })
    this.props.fetchSearchSuggestionsSuccess(null)
    this.props.fetchSearch(searchString, materialType, filtersApplied, sortBy, UILanguageId, {
      ...options,
      onFinally: () => {
        this.setState({ isSearching: false })
      }
    })
  }

  handleUseOriginalSearch = (options) => {
    const { searchString, materialType, filtersApplied, customParams } = options
    this.executeSearch(
      searchString,
      materialType,
      filtersApplied,
      this.props.sortBy,
      this.props.preferences.ui_language_id,
      { customParams }
    )
  }

  sortLanguagesByName = (languages) => {
    return languages.sort((a, b) => {
      let x = a.name
      let y = b.name
      return x < y ? -1 : x > y ? 1 : 0
    })
  }

  setShowTooltipMessage(value) {
    this.setState({
      isTooltipMessage: value
    })
  }

  changeLanguage(languageId) {
    // Get existing filters from URL to preserve them
    const { filtersApplied } = analyzeUrl(this.props.location.search)

    // Preserve existing language filter if it was set by the sidebar filters
    // Only update language filter if no language filter exists or if it only contains the default language
    const existingLanguageFilter = filtersApplied?.language?.value || []
    const shouldUpdateLanguageFilter =
      existingLanguageFilter.length === 0 ||
      (existingLanguageFilter.length === 1 &&
        existingLanguageFilter[0] === this.props.preferences.ui_language_id)

    const newFilters = JSON.stringify({
      ...(filtersApplied || {}), // Preserve existing filters
      campaign: { value: [this.state.campaignId] },
      // Only update language filter if it wasn't explicitly set by user in sidebar
      ...(shouldUpdateLanguageFilter ? { language: { value: [+languageId] } } : {})
    })
    this.props.history.replace(
      `/campaign/CA${this.state.campaignId}${getLanguageCodeById(
        languageId
      ).toUpperCase()}?type=1&filters=${newFilters}`
    )
  }

  actionBookmark(campaign, isAsset, type) {
    eventDispatch({ action: 'click', category: 'campaign_page_new', label: 'detail_bookmark' })
    const typeMaterial = 2
    if ((type === 'related' && campaign.bookmark) || (type !== 'related' && this.state.bookmark)) {
      this.props.removeBookmark(campaign.campaigns, typeMaterial)
      this.setState({ bookmark: 0 })
      if (type === 'related') {
        let oldRelated = this.state.relatedCampaigns
        campaign.bookmark = 0
        //oldRelated[campaign.campaigns] = campaign;
        oldRelated.forEach(function callback(currentCampaign, index) {
          if (currentCampaign.campaigns === campaign.campaigns) {
            oldRelated[index] = campaign
          }
        })
        this.setState({ relatedCampaigns: oldRelated })
      }
    } else {
      this.props.addBookmark(campaign.campaigns, typeMaterial)
      this.setState({ bookmark: 1 })
      if (type === 'related') {
        let oldRelated = this.state.relatedCampaigns
        campaign.bookmark = 1
        oldRelated.forEach(function callback(currentCampaign, index) {
          if (currentCampaign.campaigns === campaign.campaigns) {
            oldRelated[index] = campaign
          }
        })
        this.setState({ relatedCampaigns: oldRelated })
      }
      if (this.props.preferences.bookmark === 1) {
        this.props.postBookmarkTooltipStatus()
      }
    }
  }

  handleClickElement() {
    this.setState({ show: !this.state.show })
  }

  closeModal() {
    this.setState({ displayModalDisclaimer: false })
  }
  openModal() {
    this.setState({ displayModalDisclaimer: true })
  }

  moreDetail = (type, value) => {
    if (type === 'asset') {
      return `/${type}/AS${value.id}${getLanguageCodeById(this.props.languageId).toUpperCase()}`
    } else if (type === 'campaign') {
      return `/${type}/CA${value.campaigns}${getLanguageCodeById(
        this.props.languageId
      ).toUpperCase()}`
    }
  }

  openGuideUrl(url) {
    window.open(url)
  }

  convertToDataModal(assetDescription) {
    let assetDescriptionOriginal = assetDescription
    assetDescriptionOriginal = assetDescriptionOriginal.replace(/\r\n|\n|\r/gm, '<br>')
    assetDescriptionOriginal = assetDescriptionOriginal.replace(/"/gm, "'")
    const string = assetDescriptionOriginal.match(/\*\*\{[^}]*\}\}*.[^}]*\}\}/g)

    if (string !== null) {
      string.map((element, index) => {
        const splitToModal = string[index].split(/{{/)

        let title = splitToModal[1].replace(/}\}/, '')
        let content = splitToModal[2].replace(/\*\*/, '')
        content = content.replace(/\}\}/, '')

        const modalLink = `<a class="link-modal" href="#" data-modal="${content}">${title}</a>`

        let replaced = assetDescriptionOriginal.replace(/\*\*\{[^}]*\}\}*.[^}]*\}\}/, modalLink)
        replaced = replaced.replace(/\*\*/, '')

        assetDescriptionOriginal = replaced
        return undefined
      })
      return assetDescriptionOriginal.replace(/<br>/gm, '\n')
    }

    return assetDescription.replace(/<br>/gm, '\n')
  }

  render() {
    const { campaignId, campaignInfo, campaignPreview, show } = this.state
    const { fullDetailsCampaign } = this.props
    const languages = []
    if (fullDetailsCampaign.languages !== undefined && fullDetailsCampaign.languages !== false) {
      languages.push(
        fullDetailsCampaign.languages
          .filter((l) => l.active === 1)
          .sort((a, b) => {
            let x = a.name
            let y = b.name
            return x < y ? -1 : x > y ? 1 : 0
          })
      )
    }

    if (campaignInfo && campaignPreview) {
      let benefitsInput = campaignInfo.benefits
      let htmlToReactParser = new HtmlToReactParser()
      let mainBenefitsElement = benefitsInput ? this.convertToDataModal(benefitsInput) : null
      let descriptionElement = this.convertToDataModal(campaignInfo.description)
      let disclaimersElement = campaignInfo.disclaimers
        ? htmlToReactParser.parse(campaignInfo.disclaimers)
        : ''

      return (
        <div className="campaignPage">
          <section className="header-campaign-detail bg-white">
            <div className="container p-0">
              <GenericModal
                isVisible={this.state.isModalVisible}
                currentContent={this.state.currentContent}
                alignButtons="left"
                closable={true}
                title={this.state.title}
                handleCancel={this.handleCancel}
              />
              <RsModal
                closeFn={() => this.closeModal()}
                title={this.context.t('campaignPage').campaignDetail.modalDisclaimer.title}
                customClass="rs-modal-disclaimers"
                modal={this.state.displayModalDisclaimer}
                loading={false}
                icon="legal"
              >
                <div className="modal-disclaimers-text">
                  <Text size="14" color="greyish-brown2">
                    {disclaimersElement || ''}
                  </Text>
                </div>
              </RsModal>

              <div className="row">
                <div className="col-lg-6">
                  <GoBackArrowButtonSafe defaultUrl="/campaigns" />

                  <div className="titles-top-space"></div>

                  {isCampaignEmbargoed(this.props.fullDetailsCampaign) && (
                    <Title
                      customClass="embargo-legend"
                      type="h2"
                      size="24"
                      color="rouge"
                      text={`${this.context.t('card').embargo}:`}
                    />
                  )}

                  <Title
                    type="h1"
                    size="36"
                    color="ocean-blue"
                    customClass="mt-0"
                    text={campaignInfo.name}
                  />

                  <Title
                    customClass="h2-presentation"
                    type="h2"
                    size="24"
                    color="greyish-brown2"
                    text={this.context.t('campaignPage').campaignDetail.overview}
                  />
                  <NewText
                    size="16"
                    color="greyish-brown2"
                    customClass="overview-text m-0"
                    htmlRef={this.htmlDescriptionRef}
                  >
                    {descriptionElement}
                  </NewText>

                  {mainBenefitsElement && (
                    <div className="main-benefits">
                      <Title
                        type="h2"
                        size="24"
                        color="greyish-brown2"
                        text={this.context.t('campaignPage').campaignDetail.mainBenefits}
                      />
                      <NewText
                        size="16"
                        color="greyish-brown2"
                        customClass="main-benefits-text"
                        htmlRef={this.htmlMainBenefitsRef}
                      >
                        {mainBenefitsElement}
                      </NewText>
                    </div>
                  )}

                  {campaignInfo.disclaimers !== null && (
                    <Text
                      size="16"
                      color="brown-grey"
                      customClass="disclaimer-text"
                      onClick={() => this.openModal()}
                    >
                      {this.context.t('campaignPage').campaignDetail.disclaimer}
                    </Text>
                  )}

                  {campaignInfo.guide_url && (
                    <div className="campaign-guide">
                      <Icon iconName="campaign-guide"></Icon>
                      <div className="guide-text">
                        {
                          // eslint-disable-next-line jsx-a11y/anchor-is-valid

                          <a
                            onClick={() => {
                              eventDispatch({
                                action: 'click',
                                category: 'campaign_page_new',
                                label: 'detail_playbook'
                              })
                              this.openGuideUrl(campaignInfo.guide_url)
                            }}
                          >
                            <Title
                              type="h2"
                              size="18"
                              color="ocean-blue"
                              text={this.context.t('campaignPage').downloadGuideCampaign}
                            />
                          </a>
                        }
                        <Text size="14" color="greyish-brown2">
                          {this.context.t('campaignPage').downloadDetailCampaign}
                        </Text>
                      </div>
                    </div>
                  )}

                  <div className="d-flex align-items-center mt-15">
                    <div className="language-selector">
                      <RsSelect
                        items={languages[0]}
                        optionValue="id"
                        optionName="name"
                        selectedOption={campaignPreview.language_id}
                        onChange={(e) => {
                          eventDispatch({
                            action: 'click',
                            category: 'campaign_page_new',
                            label: 'detail_changeLang'
                          })
                          this.changeLanguage(e.target.value)
                        }}
                      />
                      <Text size="12" color="greyish-brown2" customClass="choose-language-text">
                        {this.context.t('assetsPage').chooseLanguage}
                      </Text>
                    </div>
                    <div className="share-container">
                      <RsButtonShare
                        eventCta="detail_share"
                        text={this.context.t('share')}
                        positionTooltip="bottom"
                      />
                    </div>
                  </div>
                </div>

                <div className="col-lg-5 offset-1">
                  <div className="campgn-thumb">
                    {getCookies()['unicorn-access-token'] && (
                      <div className="campaign-bookmark">
                        <RsTooltip
                          content={this.context.t('card').bookmark}
                          position="left"
                          delay={1000}
                          delayOff={6000}
                          animation
                          iconClose={false}
                          show={this.state.bookmark === 1}
                        >
                          <Icon
                            iconName={`detail-bookmark${this.state.bookmark && '-active'}`}
                            onClick={() => {
                              this.actionBookmark(this.props.fullDetailsCampaign)
                            }}
                          />
                        </RsTooltip>
                      </div>
                    )}
                    <img
                      className="campaign-preview-image"
                      src={campaignPreview.preview_url}
                      alt={this.context.t('card').bookmark}
                    />
                  </div>
                </div>
              </div>
            </div>
          </section>

          <section className="body-campaign-detail bg-very-very-light-gray-3">
            <div className="container">
              <div className="search-in-component">
                <div className="search-in-component-title">
                  <Title
                    type="h2"
                    size="36"
                    color="ocean-blue"
                    text="Search for assets in this campaign"
                  />
                  <Text size="20" color="carbon" customClass="mt-0">
                    Search and download assets to drive customer demand.
                  </Text>
                </div>
                <LibrarySearchBarContainer>
                  <SearchTip />
                  <SearchBarLibraryStyles
                    storedSearchValue={this.props.searchValue}
                    materialType={1}
                    stayOnCurrentPage={true}
                    campaignId={this.state.campaignId}
                    showMenu={false}
                  />

                  <YourSearchResults
                    assets={this.props.assets}
                    campaigns={[]}
                    resources={[]}
                    totalsQuantity={this.props.assets ? this.props.assets.length : 0}
                    showResouces={false}
                    searchType="assets"
                  />

                  <SearchSuggestions
                    suggestions={this.props.suggestions}
                    searchValue={this.props.searchValue}
                    isLoading={this.state.isSearching}
                    onUseOriginalSearch={this.handleUseOriginalSearch}
                  />
                </LibrarySearchBarContainer>
              </div>
              <div className="row">
                <Text size="16" color="carbon">
                  {this.props.assets ? this.props.assets.length : 0} {this.context.t('matches')}
                </Text>
              </div>

              <div className="row">
                <FiltersContextProvider>
                  <div className="results-all-container-filters w-260">
                    <Filters />
                  </div>

                  <div className="results-all-items">
                    <div className="elements-wrapper">
                      <Title
                        customClass="mt-0"
                        type="h1"
                        size="30"
                        color="greyish-brown2"
                        text={this.context.t('campaignPage').assetsCampaign}
                      />
                      <Assets
                        title={this.context.t('assetsPage').otherTitle}
                        onClick={this.handleClickElement.bind(this)}
                        assets={this.props.assets}
                        isHome={false}
                        savePage={true}
                        visible={show}
                        languageId={getLanguageIdByCode(this.props.lang)}
                        multipleDownloadEvent="detail_bulkDownload"
                        bookmarkEvent="detail_CampaignList_bookmark"
                        detailsEvent="detail_CampaignList_details"
                        paginationEvent="detail_CampaignList_pagination_"
                        downloadEvent="detail_CampaignList_download"
                        downloadBulkEvent="detail_CampaignBulk_Download"
                      >
                        <FiltersChips />
                      </Assets>
                    </div>
                  </div>
                </FiltersContextProvider>
              </div>
            </div>
          </section>

          {this.state.relatedCampaigns.length > 0 && (
            <section className="related-section bg-very-very-light-gray-3">
              <div className="container p-0">
                <div className="section-separator" />
                <Title
                  type="h1"
                  size="30"
                  color="greyish-brown2"
                  text={this.context.t('campaignPage').seeRelatedCampaign}
                />
                <Carrousel
                  data={this.state.relatedCampaigns}
                  action={this.actionBookmark}
                  numberOfCards={4}
                  recommended={false}
                  lang={this.props.lang}
                  context={this.context}
                  actionUrl={''}
                  bookmarks={this.props.bookmarksCampaign}
                  moreDetail={this.moreDetail}
                  bookmarkEvent={'detail_relatedCampaign_bookmark'}
                  moreDetailEvent={'detail_relatedCampaign_details'}
                  isHome={false}
                  campaignDetail={true}
                />
              </div>
            </section>
          )}
        </div>
      )
    } else {
      return <RsLoader />
    }
  }
}

const mapStateToProps = (state) => {
  return {
    assets: state.reducer.assets,
    fullDetailsCampaign: state.reducer.fullDetailsCampaign,
    bookmarksCampaign: state.reducer.bookmarksCampaign,
    preferences: state.reducer.preferences,
    lang: state.i18nState.lang,
    filters: state.reducer.filters,
    filterApplied: state.reducer.filterApplied,
    searchValue: state.reducer.searchValue,
    sortBy: state.reducer.sortBy,
    suggestions: state.reducer.suggestions
  }
}

CampaignPageNew.contextTypes = {
  t: PropTypes.func.isRequired
}

export default connect(mapStateToProps, actions)(CampaignPageNew)
