import formatDateService from '../utils/formatDateService'
import { API_URL, LOGIN_URL, LOGOUT_URL } from '../constants'
import merge from 'lodash/fp/merge'
import { setLanguage } from 'redux-i18n'
import {
  getLanguageCodeById,
  Fetch,
  encodeKeyword,
  sessionStorageShowNewsletter,
  getCookiesPermissions,
} from '../helpers'
import { getAccessToken, deleteCookie } from '../Cookies'
import fileDownload from 'js-file-download'
import ReactGA from 'react-ga'
import AdobeAnalytics from 'src/v2/intances/AdobeAnalytics'

export async function createArrayAssetsSearch(assets) {
  const token = getAccessToken()

  let data = await Promise.all(
    assets.map(async (asset) => {
      const resp = await fetch(`${API_URL}/asset/get/${asset.id}`, {
        method: 'GET',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      })
        .then((response) => response.json())
        .then((jsondata) => jsondata.data)
      return resp
    })
  )
  return data
}

export async function createArrayAssetsInfoSearch(assets) {
  //${asset.id}
  const token = getAccessToken()

  var ids = assets.map(function (asset) {
    return asset.id
  })
  // console.log('IDS', ids)

  const resp = await fetch(`${API_URL}/asset/get/all/?filters={"id":[${ids}]}`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
  })
    .then((response) => response.json())
    .then((jsondata) => jsondata.data)
  return resp
}

async function createArrayCampaignsSearch(campaigns) {
  //  let arrCampaigns = [];
  //  if(campaigns.length > 0){
  //     campaigns.map(campaign => arrCampaigns.push(fetchCampaignsInfoSearch(campaign.id)))
  //  }
  //  return arrCampaigns;

  const token = getAccessToken()

  let data = await Promise.all(
    campaigns.map(async (campaign) => {
      const resp = await fetch(`${API_URL}/campaign/get/${campaign.id}`, {
        method: 'GET',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      })
        .then((response) => response.json())
        .then((jsondata) => jsondata.data)
      return resp
    })
  )

  return data
}

async function createArrayCampaignsInfoSearch(campaigns) {
  //${campaigns.id}
  const token = getAccessToken()

  var ids = campaigns.map(function (campaign) {
    return campaign.id
  })

  const resp = await fetch(`${API_URL}/campaign/get/all/?filters={"id":[${ids}]}`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
  })
    .then((response) => response.json())
    .then((jsondata) => jsondata.data)
  return resp
}

async function createArrayResourcesSearch(resources) {
  const token = getAccessToken()

  let data = await Promise.all(
    resources.map(async (resource) => {
      const resp = await fetch(`${API_URL}/resource/get/${resource.id}`, {
        method: 'GET',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      })
        .then((response) => response.json())
        .then((jsondata) => jsondata.data)
      return resp
    })
  )

  return data
}

async function createArrayResourcesInfoSearch(resources) {
  //${campaigns.id}
  const token = getAccessToken()

  var ids = resources.map(function (resource) {
    return resource.id
  })

  const resp = await fetch(`${API_URL}/resource/get/all/?filters={"id":[${ids}]}`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
  })
    .then((response) => response.json())
    .then((jsondata) => jsondata.data)
  return resp
}

export const fetchAuthLogin = (user, pass) => (dispatch) => {
  // const dataResponse = {
  //     token_type: "Bearer",
  //     expires_in: 31622400,
  //     access_token: "*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
  //     refresh_token: "def50200d24975f61da53f418112fafadfe734d15533eb518ae0ab408c9a4ad16264e46db63fad9c04e8d9f7e25ea665870f272c16a559a6d8b3248a0620f357d563c7655526503b14aacc1652faf39b97942336652dfcc5e3b1c91d4dff23a13261e9ef61359e9b94700bfa77c089419694721ec0d375ca7a3a3f4254d49bda31b7241e5ce1f74ddb4e939b382663e4a1483fd78a54ce0ba7c295d19d3742aa997c59fc42ed1f19185bbdb66956eae0ead9dee55b7fe62a0cc74c871fb40c742d60633ba1d2d05edb26b30da06f7314f353453b6db1bfd8dd975a192bac75db8901a257324a4d21923048467854158ba98241855c6c753699c36d017b2f01fc960be4e060c8a618ca439e66926964d30b7f195615a02bdbb76dbf07c8d658d0a361abe593af590a42895d68391737cc8e8558e4627f4f2499b90b9b953201a67c982e785de5b1a171cf4a55c2b2ab4445019c9878a6deac4f7297bf3541d9745410f96b"
  // }

  let dataResponse

  const dataLogin = {
    grant_type: 'password',
    client_id: '2',
    client_secret: 'E8WPAPuALsU2dQAxAIE8K2RhO5l9IqH00LGrwjUi',
    username: user,
    password: pass,
    scope: '*',
  }
  fetch(`${API_URL}/login`, {
    method: 'post',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
    },
    body: JSON.stringify(dataLogin),
  })
    .then((response) => response.json())
    .then((jsondata) => {
      dataResponse = jsondata
      if (dataResponse.error) {
        alert(dataResponse.error_description)
        return false
      } else {
        localStorage.setItem('auth', JSON.stringify(dataResponse))
        dispatch(authLoginSuccess(dataResponse))
      }
    })
    .catch((error) => {
      console.log('Error executing auth service ' + error + '')
      window.location.href = LOGOUT_URL
    })
}

export const fetchPreferencesUser = () => (dispatch) => {
  //const token = getAccessToken();
  const token = getAccessToken()
  fetch(`${API_URL}/userConfig`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
  })
    .then((response) => response.json())
    .then((jsondata) => {
      if (jsondata.data) {
        const suscribed = jsondata.data.suscribe_newsletter
        !sessionStorageShowNewsletter.get() &&
          sessionStorageShowNewsletter.set(suscribed === 1 ? false : true)
      }

      const { ui_language_id } = jsondata.data
      dispatch(fetchPreferencesUserSuccess(jsondata))
      dispatch(setLanguage(getLanguageCodeById(ui_language_id)))
      AdobeAnalytics.load(ui_language_id)
    })

    .catch((error) => {
      console.log('Error fetching user preferences' + error)

      window.location.href = LOGOUT_URL
    })
}

export const eventSend = (action, category, label) => {
  const keys = getCookiesPermissions()
  if (!keys.analytics && !window['ga-disable-UA-157822753-9']) {
    window['ga-disable-UA-157822753-9'] = true
  }

  if (keys.analytics && window['ga-disable-UA-157822753-9']) {
    window['ga-disable-UA-157822753-9'] = false
  }
  if (keys.analytics) {
    ReactGA.event({
      action,
      category,
      label,
    })
  }
}

export const fetchPreferencesUserSuccess = (data) => ({
  type: 'FETCH_PREFERENCES_SUCCESS',
  data,
})

export const authLoginSuccess = (data) => ({
  type: 'FETCH_AUTH_SUCCESS',
  data,
})

export const fetchLogOut = () => (dispatch) => {
  const token = getAccessToken()
  deleteCookie()
  fetch(`${API_URL}/oauth/logout`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
  })
    .then((response) => response.json())
    .then((jsondata) => dispatch(fetchLogOutSuccess(jsondata)))
    .catch((error) => {
      console.log('Error logging out' + error)
    })
}

export const fetchLogOutSuccess = () => ({
  type: 'LOG_OUT',
})

export const cleanListAssets = () => ({
  type: 'CLEAN_LIST_ASSETS',
})

export const cleanListCampaigns = () => ({
  type: 'CLEAN_LIST_CAMPAIGNS',
})

export const cleanListResources = () => ({
  type: 'CLEAN_LIST_RESOURCES',
})

export const cleanListNewFilters = () => ({
  type: 'CLEAN_LIST_NEW_FILTERS',
})

export const cleanFilterApplied = () => ({
  type: 'CLEAN_FILTER_APPLIED',
})

export const cleanSearch = (data) => ({
  type: 'CLEAN_SEARCH',
  data,
})

export const cleanListFavourites = (data) => ({
  type: 'CLEAN_LIST_FAVOURITES',
})

export const fetchAssetsInfoSuccess = (data) => ({
  type: 'FETCH_ASSET_INFO_SUCCESS',
  data,
})
export const fetchDownloadAssetsInfoSuccess = (data) => ({
  type: 'FETCH_DOWNLOAD_ASSET_INFO_SUCCESS',
  data,
})
export const fetchDownloadResourcesInfoSuccess = (data) => ({
  type: 'FETCH_DOWNLOAD_RESOURCE_INFO_SUCCESS',
  data,
})

export const abortControllerAssetsOrCampaigns = (data) => ({
  type: 'ABORT_CONTROLLER_ASSET_OR_CAMPAIGNS',
  data,
})

export const fetchAssetSuccess = (data) => ({
  type: 'FETCH_ASSET_SUCCESS',
  data,
})

export const fetchCampaignsInfoSuccess = (data) => ({
  type: 'FETCH_CAMPAIGNS_INFO_SUCCESS',
  data,
})

export const fetchCampaignsSuccess = (data) => ({
  type: 'FETCH_CAMPAIGNS_SUCCESS',
  data,
})

export const fetchResourcesInfoSuccess = (data) => ({
  type: 'FETCH_RESOURCES_INFO_SUCCESS',
  data,
})

export const fetchResourcesSuccess = (data) => ({
  type: 'FETCH_RESOURCES_SUCCESS',
  data,
})

export const fetchNewFiltersSuccess = (data) => ({
  type: 'FETCH_NEW_FILTERS_SUCCESS',
  data,
})

export const fetchSearchSuggestionsSuccess = (data) => ({
  type: 'FETCH_SEARCH_SUGGESTIONS_SUCCESS',
  data,
})

export const clearAssets = () => ({
  type: 'CLEAR_ASSETS',
})

export const clearCampaigns = () => ({
  type: 'CLEAR_CAMPAIGNS',
})

export const clearResources = () => ({
  type: 'CLEAR_RESOURCES',
})

export const clearNewFilters = () => ({
  type: 'CLEAR_NEW_FILTERS',
})

export const fetchAssetInfo = (assets) => (dispatch) => {
  createArrayAssetsInfoSearch(assets).then((data) => {
    dispatch(fetchAssetsInfoSuccess(data))
  })
}

export const fetchCampaignInfo = (campaigns) => (dispatch) => {
  createArrayCampaignsInfoSearch(campaigns).then((data) => {
    dispatch(fetchCampaignsInfoSuccess(data))
  })
}

export const fetchResourceInfo = (resources) => (dispatch) => {
  createArrayResourcesInfoSearch(resources).then((data) => {
    dispatch(fetchResourcesInfoSuccess(data))
  })
}

let searchQueue = []
let activeSearch = false

export const buildSearchQueryString = (params) => {
  const {
    material,
    strSearch,
    orderBy,
    filterApplied,
    customParams = {}
  } = params;

  const queryParts = [];

  queryParts.push(`type=${material}`);

  if (strSearch !== null && strSearch !== '' && strSearch !== undefined) {
    queryParts.push(`keyword=${encodeURIComponent(strSearch)}`);
  }



  // Sorting
  if (orderBy !== null) {
    switch (orderBy) {
      case '1':
        queryParts.push('sort=relevance');
        break;
      case '2':
        queryParts.push('sort=date', 'order=asc');
        break;
      case '3':
        queryParts.push('sort=date', 'order=desc');
        break;
    }
  }

  // Filters
  if (filterApplied && JSON.stringify(filterApplied) !== '{}') {
    if (filterApplied.filters !== undefined) {
      queryParts.push(`filters=${JSON.stringify(filterApplied.filters.value[0])}`);
    } else {
      queryParts.push(`filters=${JSON.stringify(filterApplied)}`);
    }
  }

  // Add any custom parameters
  Object.entries(customParams).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      queryParts.push(`${key}=${encodeURIComponent(value)}`);
    }
  });

  return '?' + queryParts.filter(Boolean).join('&');
};

export const fetchSearch = (strSearch, material, filterApplied, orderBy, UILanguageId, options = {}) => (dispatch) => {
  if (activeSearch) {
    searchQueue = [strSearch, material, filterApplied, orderBy, UILanguageId, options];
    return;
  }


  activeSearch = true;
  dispatch(clearAssets());
  dispatch(clearCampaigns());
  dispatch(clearResources());

  const token = getAccessToken();

  // Build query string with all parameters including any custom ones from options
  const queryString = buildSearchQueryString({
    material,
    strSearch,
    UILanguageId,
    orderBy,
    filterApplied,
    customParams: options.customParams
  });

  const requestOptions = {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    ...options.fetchOptions
  };

  const abortController = new AbortController();
  requestOptions.signal = abortController.signal;

  fetch(API_URL + '/search' + queryString, requestOptions)
    .then((response) => response.json())
    .then((jsondata) => {
      if (searchQueue.length === 0) {
        if (options.onSuccess) {
          options.onSuccess(jsondata);
        }

        if (jsondata.data.assets) {
          dispatch(fetchAssetSuccess(jsondata.data.assets));
        }

        if (jsondata.data.campaigns) {
          dispatch(fetchCampaignsSuccess(jsondata.data.campaigns));
        }

        if (jsondata.data.resources) {
          dispatch(fetchResourcesSuccess(jsondata.data.resources));
        }

        if (jsondata.data?.filterConfig?.data) {
          dispatch(fetchNewFiltersSuccess(Object.values(jsondata.data.filterConfig.data)));
        } else {
          dispatch(fetchNewFiltersSuccess([]));
        }

        // Handle suggestions if present
        // Check if no_suggestion was requested
        if (options.customParams && options.customParams.no_suggestion) {
          dispatch(fetchSearchSuggestionsSuccess(null));
        } else if (jsondata.data?.suggestions) {
          dispatch(fetchSearchSuggestionsSuccess(jsondata.data.suggestions));
        } else {
          dispatch(fetchSearchSuggestionsSuccess(null));
        }

        activeSearch = false;
      } else {
        activeSearch = false;
        const searchValues = [...searchQueue];
        searchQueue = [];
        fetchSearch(...searchValues)(dispatch);
      }
    })
    .catch((error) => {
      if (options.onError) {
        options.onError(error);
      }
      activeSearch = false;
    })
    .finally(() => {
      if (options.onFinally) {
        options.onFinally();
      }
    });

  dispatch(abortControllerAssetsOrCampaigns(abortController));
};

export const clearSearchQueue = () => (dispatch) => {
  searchQueue = []
}

function getParentsAndChildren(data) {
  let children = {
    audience: {},
    placement: {},
    asset_type: {},
    device: {},
    product: {},
  }
  let parents = {}

  for (const prop in data) {
    let nameMaterial = data[prop].name_default
    nameMaterial = nameMaterial.replace(' ', '_')
    nameMaterial = nameMaterial.toLowerCase()
    let filters = data[prop].filters
    filters.map((filter) => {
      let idParent = filter.id
      if (filter.filters.length > 0) {
        if (!parents[nameMaterial]) {
          parents[nameMaterial] = [idParent]
        } else {
          parents[nameMaterial].push(idParent)
        }

        filter.filters.map((child) => {
          let id = child.id
          if (!children[nameMaterial][idParent]) {
            children[nameMaterial][idParent] = [id]
          } else {
            children[nameMaterial][idParent].push(id)
          }
        })
      }
    })
  }
  return { parents: parents, children: children }
}

export const setParentsAndChildren = (data) => ({
  type: 'SET_PARENTS_CHILDREN',
  data,
})

// export const fetchFilter = campaingId => dispatch => {
export const fetchFilter = (materialType, searchKeyword, UILanguage, campaingId) => (dispatch) => {
  // const token = getAccessToken();
  const token = getAccessToken()
  // let query = campaingId ? `?campaign=${campaingId}` : ''

  let material = materialType ? `material=${materialType}` : 'material=0'
  let keyword = searchKeyword ? encodeKeyword('keyword', searchKeyword) : ''
  let language = UILanguage ? `&language=${UILanguage}` : ''
  let campaign = campaingId ? `&campaign=${campaingId}` : ''

  let query = `?${material}${keyword}${language}${campaign}`

  fetch(`${API_URL}/filterConfig` + query, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
  })
    .then((response) => response.json())
    .then((jsonData) => {
      if (jsonData.data) {
        dispatch(setParentsAndChildren(getParentsAndChildren(jsonData.data)))
        dispatch(fetchFiltersSuccess(jsonData.data))
      } else {
        dispatch(fetchFiltersSuccess([]))
      }
    })
    .catch((error) => {
      console.log('Error fetching user filters' + error + 'Possibly caused by invalid token')
      dispatch(fetchFiltersSuccess([]))
    })
}

// async function orderByCategory(filters) {
//     let arrayFilters = {}

//     filters.map(f => {
//         let nameParse = f.name_default.toLowerCase().replace(' ','_')
//         arrayFilters[nameParse] = []
//         f.filters.map(filter => {
//             arrayFilters[nameParse].push(filter.id)
//         })
//     })

//     return arrayFilters
// }

// export const fetchFiltersByCategorySuccess = (data) => ({
//     type: 'FETCH_FILTERS_CATEGORY_SUCCESS',
//     data,
// })

export const fetchFiltersSuccess = (data) => ({
  type: 'FETCH_FILTERS_SUCCESS',
  data,
})

export const clearFiltersSuccess = (data) => ({
  type: 'CLEAN_FILTERS_SUCCESS',
  data,
})

export const updateAppliedFilters = (data) => ({
  type: 'UPDATE_APPLIED_FILTERS',
  data,
})

export const fetchAssetsFiltersSuccess = (data) => ({
  type: 'FETCH_ASSETS_FILTER',
  data,
})

export const fetchFilterPages = (pages) => (dispatch) => {
  const token = getAccessToken()
  fetch(`${API_URL}/userConfig?pagination=${pages}`, {
    method: 'PUT',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
  })
    .then((response) => response.json())
    .then((jsondata) => dispatch(fetchFilterPagesSuccess(pages)))
    .catch((error) => {
      console.log('Error executing service user config ' + error + '')

      window.location.href = LOGOUT_URL
    })
}

export const fetchFilterPagesSuccess = (data) => ({
  type: 'FETCH_FILTER_PAGES',
  data,
})

export const fetchFilterData = (filter) => (dispatch) => {
  const token = getAccessToken()
  fetch(`${API_URL}/userConfig?sort_by=${filter}`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
  })
    .then((response) => response.json())
    .then((jsondata) => dispatch(fetchFilterDataSuccess(filter)))
    .catch((error) => {
      console.log('Error executing service user config ' + error + '')

      window.location.href = LOGOUT_URL
    })
}

export const fetchFilterDataSuccess = (data) => ({
  type: 'FETCH_FILTER_DATA',
  data,
})

export const fetchFilterLayout = (filter) => (dispatch) => {
  const token = getAccessToken()
  fetch(`${API_URL}/userConfig?layout=${filter}`, {
    method: 'PUT',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
  })
    .then((response) => response.json())
    .then((jsondata) => dispatch(fetchFilterLayoutSuccess(filter)))
    .catch((error) => {
      console.log('Error executing service user config ' + error + '')

      window.location.href = LOGOUT_URL
    })
}

export const fetchFilterLayoutSuccess = (data) => ({
  type: 'FETCH_FILTER_LAYOUT',
  data,
})

export const fetchSetPreferedLanguage = (lan) => (dispatch) => {
  const token = getAccessToken()
  fetch(`${API_URL}/userConfig?ui_language_id=${lan}&asset_language_id=${lan}`, {
    method: 'PUT',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
  })
    .then((response) => response.json())
    .then((jsondata) => dispatch(fetchSetPreferedLanguageSuccess(lan)))
    .catch((error) => {
      console.log('Error executing service user config ' + error + '')

      window.location.href = LOGOUT_URL
    })
}

export const fetchSetPreferedLanguageSuccess = (data) => ({
  type: 'FETCH_SET_PREFERED_LANGUAGE',
  data,
})

export const fetchSetOnboarding = () => (dispatch) => {
  // optimista
  dispatch(fetchSetOnboardingSuccess())

  const token = getAccessToken()
  fetch(`${API_URL}/userConfig?onboarding=1`, {
    method: 'PUT',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
  }).catch((error) => {
    console.log('Error executing service user config ' + error + '')

    window.location.href = LOGOUT_URL
  })
}

export const fetchSetOnboardingSuccess = (data) => ({
  type: 'FETCH_SET_ONBOARDING',
  data,
})

export const fetchSetOnboardingLang = (value) => (dispatch) => {
  // optimista
  dispatch(fetchSetOnboardingLangSuccess())

  const token = getAccessToken()
  fetch(`${API_URL}/userConfig?onboarding_lang=` + value, {
    method: 'PUT',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
  }).catch((error) => {
    console.log('Error executing service user config ' + error + '')

    window.location.href = LOGOUT_URL
  })
}

export const fetchSetOnboardingLangSuccess = (data) => ({
  type: 'FETCH_SET_ONBOARDING_LANG',
  data,
})

export const fetchSetNewsletter = () => (dispatch) => {
  const token = getAccessToken()
  fetch(`${API_URL}/userConfig?suscribe_newsletter=1`, {
    method: 'PUT',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
  })
    .then((response) => response.json())
    .then((jsondata) => dispatch(fetchSetNewsletterSuccess()))
    .catch((error) => {
      console.log('Error executing service user config ' + error + '')
      window.location.href = LOGOUT_URL
    })
}
export const fetchSetNewsletterSuccess = (data) => ({
  type: 'FETCH_SET_NEWSLETTER',
  data,
})

export const fetchFilterSave = (id) => (dispatch) => {
  const token = getAccessToken()
  fetch(`${API_URL}/filter/user`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
  })
    .then((response) => response.json())
    .then((jsonData) => dispatch(fetchFiltersSavesSuccess(jsonData.data)))
    .catch((error) => {
      console.log('Error executing service filter user ' + error + '')
      window.location.href = '/'
    })
}

export const fetchFiltersSavesSuccess = (data) => ({
  type: 'FETCH_FILTERS_SAVE_SUCCESS',
  data,
})

export const saveFilters = (name, filtersAply, typeMaterial) => (dispatch) => {
  //aca tengo q hacer la llamada al servicios
  let filtersJson = JSON.stringify(filtersAply)

  const dataFilter = {
    name: name,
    value: filtersJson,
  }

  const token = getAccessToken()
  fetch(`${API_URL}/filter`, {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(dataFilter),
  })
    .then((response) => response.json())
    .then((jsondata) => {
      dispatch(concatFilterSaveSuccess(jsondata.data))
    })
    .catch((error) => {
      console.log('Error executing service filter' + error + '')
      window.location.href = '/'
    })
}

export const concatFilterSaveSuccess = (data) => ({
  type: 'CONCAT_FILTERS_SAVE_SUCCESS',
  data,
})

export const deleteSaveFilter = (id) => (dispatch) => {
  const token = getAccessToken()
  fetch(`${API_URL}/filter/${id}`, {
    method: 'DELETE',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
  })
    .then((response) => dispatch(removeFilterSaveSuccess(id)))
    .catch((error) => {
      console.log(error)
    })
}

export const fetchFavourites = (order) => (dispatch) => {
  const token = getAccessToken()
  let queryString = ''
  if (order !== null) {
    switch (order) {
      case '1':
        queryString += `?sort=date&order=asc`
        break
      case '2':
        queryString += `?sort=date&order=desc`
        break
      default:
        queryString += ''
    }
  }

  fetch(`${API_URL}/bookmark${queryString}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
  })
    .then((response) => response.json())
    .then((jsondata) => {
      if (jsondata.data.assets) {
        dispatch(fetchAssetSuccess(jsondata.data.assets))
      }

      if (jsondata.data.campaigns) {
        dispatch(fetchCampaignsSuccess(jsondata.data.campaigns))
      }
    })
    .catch((error) => {
      console.log('Error executing bookmarks ' + error + '')
      window.location.href = '/'
    })
}

export const rsFetchFavourites = (order) => (dispatch) => {
  const token = getAccessToken()
  return fetch(`${API_URL}/bookmark`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
  })
    .then((response) => response.json())
    .then((jsondata) => {
      if (jsondata.data.assets) {
        dispatch(fetchAssetSuccess(jsondata.data.assets))
      }

      if (jsondata.data.campaigns) {
        dispatch(fetchCampaignsSuccess(jsondata.data.campaigns))
      }
      return jsondata
    })
    .catch((error) => {
      console.log('Error executing bookmarks ' + error + '')
      window.location.href = '/'
    })
}

export const fetchFavouritesSuccess = (data) => ({
  type: 'FETCH_FAVOURITES_SUCCESS',
  data,
})
export const removeFilterSaveSuccess = (data) => ({
  type: 'REMOVE_FILTERS_SAVE_SUCCESS',
  data,
})

export const addBookmark = (id, typeMaterial) => (dispatch) => {
  const token = getAccessToken()

  const data = {
    material_id: id,
    type: typeMaterial,
  }

  fetch(`${API_URL}/bookmark/`, {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(data),
  })
    .then((response) => {
      if (typeMaterial === 1) {
        dispatch(fetchBookmarkAssetSuccess(id))
      } else if (typeMaterial === 2) {
        dispatch(fetchBookmarkCampaingSuccess(id))
      }
    })
    .catch((error) => {
      console.log('Error executing bookmark ' + error + '')
      window.location.href = '/'
    })
}

export const fetchBookmarkAssetSuccess = (data) => ({
  type: 'FETCH_BOOKMARK_ASSET_SUCCESS',
  data,
})

export const fetchBookmarkCampaingSuccess = (data) => ({
  type: 'FETCH_BOOKMARK_CAMPAING_SUCCESS',
  data,
})

export const removeBookmark = (id, typeMaterial, isBookmarksPage) => (dispatch) => {
  const token = getAccessToken()
  fetch(`${API_URL}/bookmark/${id}/${typeMaterial}`, {
    method: 'DELETE',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
  })
    .then((response) => {
      const data = { id, isBookmarksPage }
      if (typeMaterial === 1) {
        dispatch(deleteBookmarkAssetSuccess(data))
      } else if (typeMaterial === 2) {
        dispatch(deleteBookmarkCampaignSuccess(data))
      }
    })
    .catch((error) => {
      console.log('Error executing remove bookmark ' + error + '')
      window.location.href = '/'
    })
}

export const deleteBookmarkAssetSuccess = (data) => ({
  type: 'DELETE_BOOKMARK_ASSET_SUCCESS',
  data,
})

export const deleteBookmarkCampaignSuccess = (data) => ({
  type: 'DELETE_BOOKMARK_CAMPAIGN_SUCCESS',
  data,
})

export const fetchDownloadsAssets = (dateStart, dateEnd) => (dispatch) => {
  const token = getAccessToken()

  fetch(
    `${API_URL}/assetDownload?dateFrom=${formatDateService(dateStart)}&dateTo=${formatDateService(
      dateEnd
    )}`,
    {
      //fetch(`${API_URL}/assetDownload`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
    }
  )
    .then((response) => response.json())
    .then((jsondata) => {
      dispatch(fetchDownloadAssetsInfoSuccess(jsondata))
    })
    .catch((error) => {
      console.log('Error executing download assets ' + error + '')
      window.location.href = '/'
    })
}

export const fetchDownloadsResources = (dateStart, dateEnd) => (dispatch) => {
  const token = getAccessToken()

  fetch(
    `${API_URL}/resourceDownload?dateFrom=${formatDateService(
      dateStart
    )}&dateTo=${formatDateService(dateEnd)}`,
    {
      //fetch(`${API_URL}/resourceDownload`, {

      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
    }
  )
    .then((response) => response.json())
    .then((jsondata) => {
      dispatch(fetchDownloadResourcesInfoSuccess(jsondata))
    })
    .catch((error) => {
      console.log('Error executing download resources ' + error + '')
      window.location.href = '/'
    })
}

export const deleteDownloadsResources = (id) => (dispatch) => {
  const token = getAccessToken()

  fetch(`${API_URL}/resourceDownload/${id}`, {
    method: 'DELETE',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
  })
    .then((response) => response.json())
    .then((jsondata) => {
      dispatch(deleteDownloadsResourcesSuccess(jsondata))
    })
    .catch((error) => {
      console.log('Error al eliminar download resources ' + error + '')
      window.location.href = '/'
    })
}

export const deleteDownloadsAssets = (id) => (dispatch) => {
  const token = getAccessToken()

  fetch(`${API_URL}/assetDownload/${id}`, {
    method: 'DELETE',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
  })
    .then((response) => response.json())
    .then((jsondata) => {
      dispatch(deleteDownloadsAssetsSuccess(jsondata))
    })
    .catch((error) => {
      console.log('Error al eliminar download assets ' + error + '')
      window.location.href = '/'
    })
}

export const deleteDownloadsAssetsSuccess = (data) => ({
  type: 'DELETE_DOWNLOAD_ASSET_SUCCESS',
  data,
})

export const deleteDownloadsResourcesSuccess = (data) => ({
  type: 'DELETE_DOWNLOAD_RESOURCE_SUCCESS',
  data,
})

export const setSearchValue = (data) => ({
  type: 'SET_SEARCH_VALUE',
  data,
})

export const cleanSearchValue = () => ({
  type: 'CLEAN_SEARCH_VALUE',
})

export const addDownloadAssets = (assets, signal) => (dispatch) => {
  postDownloadAssetList(assets, signal)
    .then((data) => {
      dispatch(addDownloadAssetsSuccess(data))
    })
}

async function postDownloadAssetList(assets, signal) {
  const token = getAccessToken()

  let data = await Promise.all(
    assets.map(async (asset) => {
      const asset_json = asset.json ? `&json=${asset.json}` : ''
      const queryString = `?asset_id=${asset.asset_id}&users_id=${asset.users_id}&language_id=${asset.language_id}${asset_json}`
      const resp = await fetch(`${API_URL}/assetDownload/${queryString}`, {
        signal,
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      })
        .then((response) => response.json())
        .then((jsondata) => jsondata.data)
      return resp
    })
  )

  return data
}

export const addDownloadAssetsSuccess = (data) => ({
  type: 'ADD_DOWNLOAD_ASSETS_SUCCESS',
  data,
})

export const addCustomizationAssets = (assets) => (dispatch) => {
  postCustomizationAssetList(assets).then((data) => {
    dispatch(addCustomizationAssetsSuccess(data))
  })
}

async function postCustomizationAssetList(assets) {
  const token = getAccessToken()

  let data = await Promise.all(
    assets.map(async (asset) => {
      const asset_json = asset.json ? `&json=${asset.json}` : ''
      const queryString = `?asset_id=${asset.asset_id}&users_id=${asset.users_id}&language_id=${asset.language_id}${asset_json}`
      const resp = await fetch(`${API_URL}/assetCustomizable/${queryString}`, {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      })
        .then((response) => response.json())
        .then((jsondata) => jsondata.data)
      return resp
    })
  )

  return data
}

export const addCustomizationAssetsSuccess = (data) => ({
  type: 'ADD_CUSTOMIZATION_ASSETS_SUCCESS',
  data,
})

export const postBookmarkTooltipStatus = () => (dispatch) => {
  const token = getAccessToken()
  fetch(`${API_URL}/userConfig?bookmark=0`, {
    method: 'PUT',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
  })
    .then((response) => response.json())
    .then((jsondata) => dispatch(postBookmarkTooltipStatusSuccess(jsondata.data)))
    .catch((error) => {
      console.log('Error executing userConfig ' + error + '')

      window.location.href = LOGOUT_URL
    })
}

export const postBookmarkTooltipStatusSuccess = (data) => ({
  type: 'POST_BOOKMARK_TOOLTIP_STATUS_SUCCESS',
  data,
})

export const fetchCampaign = (campaignId) => (dispatch) => {
  const token = getAccessToken()
  if (campaignId !== null) {
    fetch(`${API_URL}/campaign/get/${campaignId}`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
    })
      .then((response) => response.json())
      .then((jsondata) => dispatch(getCampaign(jsondata.data)))
  }
}

export const getCampaign = (data) => ({
  type: 'CAMPAIGN_DETAILS',
  data,
})

export const fetchAsset = (assetId) => (dispatch) => {
  const token = getAccessToken()
  if (assetId !== null) {
    fetch(`${API_URL}/asset/get/${assetId}`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
    })
      .then((response) => response.json())
      .then((jsondata) => dispatch(getAsset(jsondata.data)))
  }
}

export const getAsset = (data) => ({
  type: 'ASSET_DETAILS',
  data,
})

export const fetchAssetsByCampaign = (campaignId) => (dispatch) => {
  const token = getAccessToken()
  if (campaignId !== null) {
    fetch(
      `${API_URL}/search?type=1&sort=date&order=desc&filters={%22campaign%22:{%22value%22:[${campaignId}]}}`,
      {
        method: 'GET',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      }
    )
      .then((response) => response.json())
      .then((jsondata) => {
        if (jsondata.data) {
          dispatch(getAssetByCampaign(jsondata.data.assets))
        }
      })
  }

  /* let data = [{id:8}, {id:11}, {id:14}]
    createArrayAssetsSearch(data)
    .then( data => {
        dispatch(fetchAssetsInfoSuccess(data))
    })*/
}

export const getAssetByCampaign = (data) => ({
  type: 'ASSET_BY_CAMPAIGN',
  data,
})

export const fetchCampaignsByAsset = (assetId) => (dispatch) => {
  const token = getAccessToken()
  if (assetId !== null) {
    fetch(`${API_URL}/asset/${assetId}/campaigns`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
    })
      .then((response) => response.json())
      .then((jsondata) => {
        dispatch(getCampaignByAsset(jsondata.data))
      })
  }
}

export const getCampaignByAsset = (data) => ({
  type: 'CAMPAIGN_BY_ASSET',
  data,
})

export const fetchEvergreenFields = (assetId) => (dispatch) => {
  getFullAsset(assetId).then((data) => dispatch(getEvergreenFields(data)))
}

export function b64toBlob(b64Data, contentType, sliceSize) {
  contentType = contentType || ''
  sliceSize = sliceSize || 512

  var byteCharacters = atob(b64Data)
  var byteArrays = []

  for (var offset = 0; offset < byteCharacters.length; offset += sliceSize) {
    var slice = byteCharacters.slice(offset, offset + sliceSize)

    var byteNumbers = new Array(slice.length)
    for (var i = 0; i < slice.length; i++) {
      byteNumbers[i] = slice.charCodeAt(i)
    }

    var byteArray = new Uint8Array(byteNumbers)

    byteArrays.push(byteArray)
  }

  var blob = new Blob(byteArrays, { type: contentType })

  return blob
}

async function getFullAsset(assetId) {
  const token = getAccessToken()
  let [ever, asset] = await Promise.all([
    fetch(`${API_URL}/asset/${assetId}/evergreenFields`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
    })
      .then((response) => response.json())
      .then((jsondata) => jsondata.data)
      .catch((err) => false),
    fetch(`${API_URL}/asset/get/${assetId}`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
    })
      .then((response) => response.json())
      .then((jsondata) => jsondata.data)
      .catch((err) => false)
  ])
  if (ever && asset) {
    delete asset.audience
    delete asset.asset_types
    return merge(ever, asset)
  } else {
    return false
  }
}

export const getEvergreenFields = (data) => ({
  type: 'EVERGREEN_FIELDS',
  data,
})

//Recommended for you Featured
export const fetchGetRecommendedForYou = () => (dispatch) => {
  const token = getAccessToken()
  fetch(`${API_URL}/recommend/download`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
  })
    .then((response) => response.json())
    .then((jsondata) => {
      if (jsondata.data) {
        let recommendedArray = Object.values(jsondata.data)
        recommendedArray.map((asset) => (asset.id = asset.asset))
        dispatch(getRecommendedForYouSuccess(recommendedArray.reverse()))
      } else {
        dispatch(getRecommendedForYouSuccess([]))
      }
    })
    .catch((error) => {
      console.log('Error executing service recommendedforyou', error)
    })
}

export const getRecommendedForYouSuccess = (data) => ({
  type: 'GET_RECOMMENDED_FOR_YOU_STATUS_SUCCESS',
  data,
})

//Assets Featured
export const fetchGetAssetFeatured = () => (dispatch) => {
  const token = getAccessToken()
  fetch(`${API_URL}/assetFeatured`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
  })
    .then((response) => response.json())
    .then((jsondata) => dispatch(getAssetFeaturedSuccess(jsondata.data)))
    .catch((error) => {
      console.log('Error executing service assetFeatured', error)
    })
}

export const getAssetFeaturedSuccess = (data) => ({
  type: 'GET_ASSET_FEATURED_STATUS_SUCCESS',
  data,
})

export const getAssetCarrouselGuest = (data) => ({
  type: 'GET_ASSET_CARROUSEL_GUEST',
  data,
})

//Campaigns Featured
export const fetchGetCampaignsFeatured = () => (dispatch) => {
  const token = getAccessToken()
  fetch(`${API_URL}/campaignFeatured`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
  })
    .then((response) => response.json())
    .then((jsondata) => dispatch(getCampaignsFeaturedSuccess(jsondata.data)))
    .catch((error) => {
      console.log('Error executing service campaignFeatured', error)
    })
}

export const getCampaignsFeaturedSuccess = (data) => ({
  type: 'GET_CAMPAIGNS_FEATURED_STATUS_SUCCESS',
  data,
})

//Homepage Public
export const fetchGetHomePagePublic = () => (dispatch) => {
  fetch(`${API_URL}/homepage`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
    },
  })
    .then((response) => response.json())
    .then((jsondata) => dispatch(getHomePagePublicSuccess(jsondata.data)))
    .catch((error) => {
      console.log('Error executing service campaignFeatured', error)
    })
}

export const getHomePagePublicSuccess = (data) => ({
  type: 'GET_HOME_PAGE_STATUS_SUCCESS',
  data,
})

export const fetchServiceBanner = () => (dispatch) => {
  fetch(`${API_URL}/banner`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
    },
  })
    .then((response) => response.json())
    .then((jsondata) => dispatch(getServiceBannerSuccess(jsondata.data)))
    .catch((error) => {
      console.log('Error executing service banner service', error)
    })
}

export const getServiceBannerSuccess = (data) => ({
  type: 'GET_SERVICE_BANNER_STATUS_SUCCESS',
  data,
})

//Share url
export const shareShortLink = (obj) => (dispatch) => {
  const token = getAccessToken()

  return fetch(`${API_URL}/share/shortlink`, {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(obj),
  })
    .then((response) => response.json())
    .then((response) => {
      let copyHashUrl = document.createElement('input')
      let url = window.location.origin + '/share/' + response.data.hash
      document.body.appendChild(copyHashUrl)
      copyHashUrl.setAttribute('value', url)
      copyHashUrl.select()
      document.execCommand('copy')
      document.body.removeChild(copyHashUrl)
      return response
    })
}

export const getShareShortLink = (id) => (dispatch) => {
  return fetch(`${API_URL}/share/shortlink/${id}`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
    },
  })
    .then((response) => response.json())
    .then((response) => {
      return response
    })
    .catch((err) => {
      return err.json()
    })
}

export const fetchFullCampaign = (campaignId) => (dispatch) => {
  getCampaignsWithAssets(campaignId, dispatch).then((data) => dispatch(getFullCampaign(data)))
}

export const fetchAssetsAvailables = () => (dispatch) => {
  getAssetsAvailables().then((data) => dispatch(getListAssetsAvailables(data)))
}

export const setTooltipsSearch = (data) => ({
  type: 'SET_TOOLTIPS_SEARCH',
  data,
})

export const fetchTooltipsSearch = (keywordsSearch) => (dispatch) => {
  Fetch.GET(`/tooltips/${keywordsSearch}`, (res) => res)
    .then((res) => dispatch(setTooltipsSearch(res.data)))
    .catch((err) => console.log(err))
}

async function getAssetsAvailables() {
  const token = getAccessToken()

  const data = fetch(`${API_URL}/admin/asset/list`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
  })
    .then((response) => response.json())
    .then((jsondata) => createArrayAssetsSearch(jsondata))
    .catch((error) => {
      console.log('Error executing service AssetsAvailables', error)
    })

  return data
}

async function getCampaignsWithAssets(campaignId, dispatch) {
  const token = getAccessToken()
  let [campaign, assetByCampaign, relatedCampaigns] = await Promise.all([
    fetch(`${API_URL}/campaign/get/${campaignId}`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
    })
      .then((response) => response.json())
      .then((jsondata) => jsondata.data)
      .catch((err) => false),
    fetch(
      `${API_URL}/search?type=1&sort=date&order=desc&filters={%22campaign%22:{%22value%22:[${campaignId}]}}`,
      {
        method: 'GET',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      }
    )
      .then((response) => response.json())
      .then((jsondata) => dispatch(getAssetByCampaign(jsondata.data.assets)))
      .catch((err) => false),
    fetch(`${API_URL}/campaign/${campaignId}/relatedCampaigns`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
    })
      .then((response) => response.json())
      .then((jsondata) => createArrayCampaignsSearch(jsondata.data))
      .catch((jsondata) => createArrayCampaignsSearch([])),
  ])

  if (campaign && assetByCampaign) {
    let union = merge(campaign, { assetByCampaign: assetByCampaign })
    return merge(union, { relatedCampaigns: relatedCampaigns })
  } else {
    return false
  }
}

export const getFullCampaign = (data) => ({
  type: 'FULL_CAMPAIGN_DETAILS',
  data,
})

export const getListAssetsAvailables = (data) => ({
  type: 'ASSETS_LIST',
  data,
})

export const fetchCampaignDetail = (campaignId) => (dispatch) => {
  getCampaignsWithRelatedCampaigns(campaignId, dispatch).then((data) =>
    dispatch(getFullCampaign(data))
  )
}

async function getCampaignsWithRelatedCampaigns(campaignId, dispatch) {
  const token = getAccessToken()
  let [campaign, relatedCampaigns] = await Promise.all([
    fetch(`${API_URL}/campaign/get/${campaignId}`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
    })
      .then((response) => response.json())
      .then((jsondata) => jsondata.data)
      .catch((err) => false),
    fetch(`${API_URL}/campaign/${campaignId}/relatedCampaigns`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
    })
      .then((response) => response.json())
      .then((jsondata) => createArrayCampaignsSearch(jsondata.data))
      .catch((jsondata) => createArrayCampaignsSearch([])),
  ])

  if (campaign) {
    return merge(campaign, { relatedCampaigns: relatedCampaigns })
  } else {
    return false
  }
}

function download(filename, text) {
  var pom = document.createElement('a')
  pom.setAttribute('href', 'data:text/xls,' + text)
  pom.setAttribute('download', filename)

  if (document.createEvent) {
    var event = document.createEvent('MouseEvents')
    event.initEvent('click', true, true)
    pom.dispatchEvent(event)
  } else {
    pom.click()
  }
}

export const fetchMetrics = (fromDate, toDate, handleLoading) => (dispatch) => {
  const token = getAccessToken()
  fetch(
    `${API_URL}/download-activity?from=${formatDateService(fromDate)}&to=${formatDateService(
      toDate
    )}`,
    {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  )
    .then((response) => {
      if (response.status !== 200) {
        throw new Error()
      } else {
        return response.arrayBuffer()
      }
    })
    .then((data) => {
      var blob = new Blob([data], {
        type: 'application/vnd.ms-excel',
      })
      fileDownload(blob, `metrics-${formatDateService(fromDate)}-${formatDateService(toDate)}.xlsx`)
      // let link = document.createElement("a");
      // let url = window.URL.createObjectURL(blob);
      // link.href = url;
      // link.style = "visibility:hidden";
      // link.download = `metrics-${formatDateService(fromDate)}-${formatDateService(toDate)}.xlsx`;
      // document.body.appendChild(link);
      // link.click();
      // document.body.removeChild(link);

      dispatch({
        type: 'ADMIN_SEND_NOTIFICATION',
        payload: {
          type: 'success',
          message: 'Your report **has been successfully downloaded!',
        },
      })
    })
    .catch((error) => {
      dispatch({
        type: 'ADMIN_SEND_NOTIFICATION',
        payload: {
          type: 'error',
          message: "There's no data available for the selected time range. Please try changing the dates."
        }
      })
    })
    .finally(() => handleLoading())
}

export const fetchEloqua = (fromDate, toDate) => (dispatch) => {
  const token = getAccessToken()
  fetch(
    `${API_URL}/download-activity-eloqua?from=${formatDateService(fromDate)}&to=${formatDateService(
      toDate
    )}`,
    {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  )
    .then((response) => {
      if (response.status !== 200) {
        throw new Error()
      } else {
        return response.arrayBuffer()
      }
    })
    .then((data) => {
      var blob = new Blob([data], {
        type: 'application/vnd.ms-excel',
      })

      fileDownload(
        blob,
        `CDO_Ingestion-${formatDateService(fromDate)}-${formatDateService(toDate)}.xlsx`
      )

      // let link = document.createElement("a");
      // let url = window.URL.createObjectURL(blob);
      // link.href = url;
      // link.style = "visibility:hidden";
      // link.download = `CDO_Ingestion-${formatDateService(fromDate)}-${formatDateService(toDate)}.xlsx`;
      // document.body.appendChild(link);
      // link.click();
      // document.body.removeChild(link);

      dispatch({
        type: 'ADMIN_SEND_NOTIFICATION',
        payload: {
          type: 'success',
          message: 'Your report **has been successfully downloaded!',
        },
      })
    })
    .catch((error) => {
      dispatch({
        type: 'ADMIN_SEND_NOTIFICATION',
        payload: {
          type: 'error',
          message: "There's no data available for the selected time range. Please try changing the dates."
        }
      })
    })
}

export const fetchEloquaCampaigns = (fromDate, toDate) => (dispatch) => {
  const token = getAccessToken()
  fetch(
    `${API_URL}/download-campaign?from=${formatDateService(fromDate)}&to=${formatDateService(
      toDate
    )}`,
    {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  )
    .then((response) => {
      if (response.status !== 200) {
        throw new Error()
      } else {
        return response.arrayBuffer()
      }
    })
    .then((data) => {
      var blob = new Blob([data], {
        type: 'application/vnd.ms-excel',
      })

      fileDownload(
        blob,
        `CDO_Assets-Campaigns-${formatDateService(fromDate)}-${formatDateService(toDate)}.xlsx`
      )

      dispatch({
        type: 'ADMIN_SEND_NOTIFICATION',
        payload: {
          type: 'success',
          message: 'Your report **has been successfully downloaded!',
        },
      })
    })
    .catch((error) => {
      dispatch({
        type: 'ADMIN_SEND_NOTIFICATION',
        payload: {
          type: 'error',
          message: "There's no data available for the selected time range. Please try changing the dates."
        }
      })
    })
}

export const fetchNPSSurvey = () => (dispatch) => {
  const token = getAccessToken()
  fetch(`${API_URL}/survey`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
  })
    .then((response) => response.json())
    .then((jsondata) => {
      if (!jsondata.error) {
        dispatch(setSurveyInfo(jsondata.data))
      } else {
        dispatch(setSurveyInfo(null))
      }
    })
    .catch((error) => {
      dispatch(setSurveyInfo(null))
    })
}

export const setSurveyInfo = (data) => ({
  type: 'SET_SURVEY_INFO',
  data,
})

export const submitSurvey = (answer) => (dispatch) => {
  const token = getAccessToken()
  fetch(`${API_URL}/survey`, {
    method: 'PATCH',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(answer),
  })
    .then((response) => response.json())
    .then((jsondata) => {
      if (!jsondata.error) {
        dispatch(surveySent(true))
      } else {
        console.log('Error submitting survey ' + jsondata.error + '')
        dispatch(surveySent(false))
      }
    })
    .catch((error) => {
      console.log('Error submitting survey ' + error + '')
      dispatch(surveySent(false))
    })
}

export const surveySent = (data) => ({
  type: 'SURVEY_SENT',
  data,
})

export const askMeLater = () => (dispatch) => {
  const token = getAccessToken()
  fetch(`${API_URL}/survey/askmelater`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
  })
    .then((response) => response.json())
    .then((jsondata) => {
      if (!jsondata.error) {
        dispatch(setSurveyInactive(true))
      } else {
        console.log('Error executing ask me later ' + jsondata.error + '')
        dispatch(setSurveyInactive(false))
      }
    })
    .catch((error) => {
      console.log('Error executing ask me later ' + error + '')
      dispatch(setSurveyInactive(false))
    })
}

export const setSurveyInactive = (data) => ({
  type: 'SET_SURVEY_INACTIVE',
  data,
})

export const setLanguages = (ln) => (dispatch) => {
  dispatch(setLanguage(ln))
}

export const cleanFullCampaign = () => ({
  type: 'CLEAN_FULL_CAMPAIGN_DETAILS',
})
export const cleanFullAsset = () => ({
  type: 'CLEAN_FULL_ASSET_DETAILS',
})

export const changeSortBy = (value) => (dispatch) => {
  dispatch(setSortBy(value))
}

export const setSortBy = (data) => ({
  type: 'CHANGE_SORT_BY',
  data,
})

export const typeScreenGroup = (payload) => {
  return {
    type: 'TYPE_SCREEN_GROUP',
    payload
  }
}